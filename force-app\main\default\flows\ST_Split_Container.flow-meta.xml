<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Add_Open_Product_to_Collection</name>
        <label>Add Open Product to Collection</label>
        <locationX>798</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>openProductsCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_All_Products</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>indexLoopProduct</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>is_first_iteraction</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Counts</name>
        <label>Assign Product Counts</label>
        <locationX>578</locationX>
        <locationY>1466</locationY>
        <assignmentItems>
            <assignToReference>productCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Products</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>openProductCount</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>openProductsCollection</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Product_Counts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_14</name>
        <label>Assignment 14</label>
        <locationX>1194</locationX>
        <locationY>4538</locationY>
        <assignmentItems>
            <assignToReference>opportunityContainerRecalculated.Products__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Products</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>opportunityContainerRecalculated.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>oppNameCalculated</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>opportunityContainerRecalculatedCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>opportunityContainerRecalculated</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_For_Container_Cloned_To_Recalculate</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>convert_Opportunity_Id_To_Text</name>
        <label>convert Opportunity Id To Text</label>
        <locationX>1262</locationX>
        <locationY>2006</locationY>
        <assignmentItems>
            <assignToReference>varOpportunityIdText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>clone_container_for_each_child_product.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>skip_first_element</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>create_Item_History_Collection_Of_Orig_Opp</name>
        <label>create Item History Collection Of Orig Opp</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Trattativa separata {!Get_Target_Container.Name}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Opportunity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Executor__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>contactHistoryCollectionToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>itemTemplateHistoryToUse</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>populate_Template_Record_OppoContainer</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>createItemTemplateHistoryCollectionToUse</name>
        <label>create Item Template History Collection To Use</label>
        <locationX>1546</locationX>
        <locationY>2798</locationY>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Trattativa separata da {!Get_Target_Container.Name}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Opportunity__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>for_Each_OppoContaier_Cloned.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>itemTemplateHistoryToUse.Executor__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!$User.FirstName} {!$User.LastName}</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>contactHistoryCollectionToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>itemTemplateHistoryToUse</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>for_Each_Opportunity_Product_To_Reparent</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>{!currentItem_filter_for_choosed_product}
{!filter_for_choosed_product.Id}</description>
        <name>populate_collection_of_OppContainer_and_OppProduct_One_Time</name>
        <label>populate collection of OppContainer and OppProduct One Time</label>
        <locationX>1170</locationX>
        <locationY>2006</locationY>
        <assignmentItems>
            <assignToReference>opportunityContainerTemplateCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>templateRecordOpportunityContainer</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>opportunityProductToReparentCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>filter_for_choosed_product</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>clona_opportunity_container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>populate_collection_of_OppContainer_and_OppProductNoParent_To_Use</name>
        <label>populate collection of OppContainer and OppProductNoParent To Use</label>
        <locationX>1130</locationX>
        <locationY>2222</locationY>
        <assignmentItems>
            <assignToReference>opportunityContainerTemplateCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>templateRecordOpportunityContainer</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>clone_container_for_each_child_product.Parent__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>opportunityProductToReparentCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>clone_container_for_each_child_product</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>clone_container_for_each_child_product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>populate_Template_Record_OppoContainer</name>
        <label>populate Template Record OppoContainer</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>templateRecordOpportunityContainer</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Target_Container</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>templateRecordOpportunityContainer.Id</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>templateRecordOpportunityContainer.IsSetRef__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Products</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>populateCollectionContainerToUpdate</name>
        <label>populate Collection Container To Update</label>
        <locationX>1106</locationX>
        <locationY>4106</locationY>
        <assignmentItems>
            <assignToReference>opportunityContainerRecalculatedCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>opportunityContainerRecalculated</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_For_Container_Cloned_To_Recalculate</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>reparent_Opportunity_Product</name>
        <label>reparent Opportunity Product</label>
        <locationX>1986</locationX>
        <locationY>3230</locationY>
        <assignmentItems>
            <assignToReference>for_Each_Opportunity_Product_To_Reparent.Parent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>for_Each_OppoContaier_Cloned.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>save_product_reParent_and_mark_parent_Used</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>{!for_Each_Opportunity_Product_To_Reparent}</description>
        <name>save_product_reParent_and_mark_parent_Used</name>
        <label>save product reParent and mark parent Used</label>
        <locationX>1986</locationX>
        <locationY>3338</locationY>
        <assignmentItems>
            <assignToReference>productOpportunityCollectionReparentedToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>for_Each_Opportunity_Product_To_Reparent</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>iDoppoProductCollectionToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>for_Each_Opportunity_Product_To_Reparent.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>parentUsedCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>for_Each_OppoContaier_Cloned.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>for_Each_Opportunity_Product_To_Reparent</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>saveIdFirstProduct</name>
        <label>save Id First Product</label>
        <locationX>666</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>firstProduct</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_All_Products.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_All_Products</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>allOpportunitiesSelected</name>
        <choiceText>Tutte</choiceText>
        <dataType>String</dataType>
        <value>
            <elementReference>all</elementReference>
        </value>
    </choices>
    <collectionProcessors>
        <name>filter_for_choosed_product</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>filter for choosed product</label>
        <locationX>1170</locationX>
        <locationY>1898</locationY>
        <assignNextValueToReference>currentItem_filter_for_choosed_product</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>openProductsCollection</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_filter_for_choosed_product.Name</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>opportunityToSplit</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>populate_collection_of_OppContainer_and_OppProduct_One_Time</targetReference>
        </connector>
    </collectionProcessors>
    <constants>
        <name>all</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Tutte</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Check_Product_Counts</name>
        <label>Check Product Counts</label>
        <locationX>578</locationX>
        <locationY>1574</locationY>
        <defaultConnector>
            <targetReference>splitTrattative</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Zero_Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>openProductCount</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>No_Product_To_Split</targetReference>
            </connector>
            <label>Zero Open</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Stage</name>
        <label>Check Product Stage</label>
        <locationX>952</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Loop_All_Products</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Closed</defaultConnectorLabel>
        <rules>
            <name>Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_All_Products.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Open_Product_to_Collection</targetReference>
            </connector>
            <label>Open</label>
        </rules>
    </decisions>
    <decisions>
        <name>choose_To_Split_All_Product</name>
        <label>choose To Split All Product</label>
        <locationX>1106</locationX>
        <locationY>1790</locationY>
        <defaultConnector>
            <targetReference>filter_for_choosed_product</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>only One Choosed</defaultConnectorLabel>
        <rules>
            <name>all_Choosed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>allOpportunitiesSelected</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>all</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>opportunityToSplit</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>all</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>clone_container_for_each_child_product</targetReference>
            </connector>
            <label>all Choosed</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_first_iteraction</name>
        <label>is first iteraction</label>
        <locationX>798</locationX>
        <locationY>998</locationY>
        <defaultConnector>
            <targetReference>Loop_All_Products</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>first_iteraction</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>indexLoopProduct</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>saveIdFirstProduct</targetReference>
            </connector>
            <label>first iteraction</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_The_Container_Already_Used_As_Parent</name>
        <label>is The Container Already Used As Parent</label>
        <locationX>1766</locationX>
        <locationY>3014</locationY>
        <defaultConnector>
            <targetReference>is_the_Product_Without_Parent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>is NOT Used</defaultConnectorLabel>
        <rules>
            <name>Yes_Is_Used</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>parentUsedCollection</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>for_Each_OppoContaier_Cloned.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>for_Each_Opportunity_Product_To_Reparent</targetReference>
            </connector>
            <label>Yes Is Used</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_the_Product_Without_Parent</name>
        <label>is  the Product ALready Reparented</label>
        <locationX>1898</locationX>
        <locationY>3122</locationY>
        <defaultConnector>
            <targetReference>reparent_Opportunity_Product</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NO</defaultConnectorLabel>
        <rules>
            <name>YES</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>iDoppoProductCollectionToUpdate</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>for_Each_Opportunity_Product_To_Reparent.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>for_Each_Opportunity_Product_To_Reparent</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <decisions>
        <name>skip_first_element</name>
        <label>skip first element</label>
        <locationX>1262</locationX>
        <locationY>2114</locationY>
        <defaultConnector>
            <targetReference>clone_container_for_each_child_product</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Product To Skip</defaultConnectorLabel>
        <rules>
            <name>product_to_not_skip</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>varOpportunityIdText</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>firstProduct</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>populate_collection_of_OppContainer_and_OppProductNoParent_To_Use</targetReference>
            </connector>
            <label>Product To NOT skip</label>
        </rules>
    </decisions>
    <description>to be deleted</description>
    <dynamicChoiceSets>
        <name>choiceOpportunity</name>
        <collectionReference>openProductsCollection</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>Opportunity</object>
        <valueField>Name</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>productCode</name>
        <collectionReference>openProductsCollection</collectionReference>
        <dataType>String</dataType>
        <displayField>DomainType__c</displayField>
        <object>Opportunity</object>
        <valueField>Name</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>allProductsMinusOne</name>
        <dataType>Number</dataType>
        <expression>{!openProductCount}-1</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>oppNameCalculated</name>
        <dataType>String</dataType>
        <expression>CASE({!opportunityContainerRecalculated.RecordType.DeveloperName}, &apos;Agenziale&apos;, &apos;AGE&apos;, &apos;Prodotto&apos;, &apos;PR&apos;,&apos;APP&apos;, &apos;APP&apos;,
IF({!opportunityContainerRecalculated.EngagementPoint__c} == &apos;Agenzia&apos;, &apos;AGE&apos;, CASE({!opportunityContainerRecalculated.Channel__c}, &apos;Preventivatore digitale Unica&apos;, &apos;UNI&apos;, &apos;BPER&apos;, &apos;BPR&apos;,&apos;Preventivatore Previdenza&apos;,&apos;PRV&apos;,&apos;UniSalute&apos;,&apos;SAL&apos;,&apos;AS&apos;))) &amp; {!opportunityContainerRecalculated.InternalCounter__c}</expression>
    </formulas>
    <interviewLabel>ST - Split Container {!$Flow.CurrentDateTime}</interviewLabel>
    <label>ST - Split Container</label>
    <loops>
        <name>clone_container_for_each_child_product</name>
        <label>clone container for each child product</label>
        <locationX>1042</locationX>
        <locationY>1898</locationY>
        <collectionReference>openProductsCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>convert_Opportunity_Id_To_Text</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>clona_opportunity_container</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>for_Each_OppoContaier_Cloned</name>
        <label>for Each OppoContaier Cloned</label>
        <locationX>1106</locationX>
        <locationY>2690</locationY>
        <collectionReference>opportunityContainerTemplateCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>createItemTemplateHistoryCollectionToUse</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>update_Products_Reparented</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>for_Each_Opportunity_Product_To_Reparent</name>
        <label>for Each Opportunity Product To Reparent</label>
        <locationX>1546</locationX>
        <locationY>2906</locationY>
        <collectionReference>opportunityProductToReparentCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>is_The_Container_Already_Used_As_Parent</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>for_Each_OppoContaier_Cloned</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_All_Products</name>
        <label>Loop All Products</label>
        <locationX>578</locationX>
        <locationY>674</locationY>
        <collectionReference>Get_Products</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_Product_Stage</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Assign_Product_Counts</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_For_Container_Cloned_To_Recalculate</name>
        <label>loop For Container Cloned To Recalculate</label>
        <locationX>1106</locationX>
        <locationY>4214</locationY>
        <collectionReference>opportunityContainerTemplateCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>recalculate_Container_Cloned</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>update_Container_Recalculated</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>clona_opportunity_container</name>
        <label>clona opportunity container</label>
        <locationX>1106</locationX>
        <locationY>2582</locationY>
        <connector>
            <targetReference>for_Each_OppoContaier_Cloned</targetReference>
        </connector>
        <inputReference>opportunityContainerTemplateCollection</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Opportunity_Configuration</name>
        <label>Get Opportunity Configuration</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_All_Products</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Target_Container.DomainType__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ProductConfiguration__mdt</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>ProductCode__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Products</name>
        <label>Get Products</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Opportunity_Configuration</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Target_Container</name>
        <label>Get Target Container</label>
        <locationX>578</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>create_Item_History_Collection_Of_Orig_Opp</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_Container_Recalculated</name>
        <label>update Container Recalculated</label>
        <locationX>1106</locationX>
        <locationY>4730</locationY>
        <connector>
            <targetReference>Check_Splitted_Container_Status</targetReference>
        </connector>
        <inputReference>opportunityContainerRecalculatedCollection</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_Products_Reparented</name>
        <label>update Products Reparented</label>
        <locationX>1106</locationX>
        <locationY>3782</locationY>
        <connector>
            <targetReference>Handle_Opportunity_Insert_Contact_History_Flow_1</targetReference>
        </connector>
        <inputReference>productOpportunityCollectionReparentedToUpdate</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>No_Product_To_Split</name>
        <label>No Product To Split</label>
        <locationX>50</locationX>
        <locationY>1682</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>NoProductToSplit</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;La trattatativa contiene un solo prodotto oppure nessuno dei suoi prodotti è in stato &quot;In gestione&quot;: Quindi non può essere separata.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>splitTrattative</name>
        <label>split Trattative</label>
        <locationX>1106</locationX>
        <locationY>1682</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>choose_To_Split_All_Product</targetReference>
        </connector>
        <fields>
            <name>titleScreenTrattativeDaSeparare</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Separa trattative&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>textGuideScreen</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Desideri separare le linee di prodotto della trattativa?&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;Una volta separate verranno gestite come trattative singole.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>splitTrattative_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>splitTrattative_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>opportunityToSplit</name>
                    <choiceReferences>choiceOpportunity</choiceReferences>
                    <choiceReferences>allOpportunitiesSelected</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Scelta Trattativa da Separare</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>splitTrattative_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>containerChoice</name>
                    <extensionName>c:rightContainerSplitTrattative</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <elementReference>opportunityToSplit</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>FlowProductCodeViewer</name>
                    <extensionName>c:flowProductCodeViewer</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>selectedOpportunityId</name>
                        <value>
                            <elementReference>opportunityToSplit</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Target_Container</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Bundle_Products</name>
        <label>Bundle Products</label>
        <locationX>1194</locationX>
        <locationY>4430</locationY>
        <connector>
            <targetReference>Assignment_14</targetReference>
        </connector>
        <flowName>Evaluate_Bundle_Products</flowName>
        <inputAssignments>
            <name>BundleOpportunity</name>
            <value>
                <elementReference>opportunityContainerRecalculated</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ProductsList</name>
            <value>
                <elementReference>productOpportunityCollectionReparentedToUpdate</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>Products</assignToReference>
            <name>ProductsOutput</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>call_Recalculation_Sub_Flow_For_Container_Orig</name>
        <label>call Recalculation Sub Flow For Container Orig</label>
        <locationX>1106</locationX>
        <locationY>3998</locationY>
        <connector>
            <targetReference>populateCollectionContainerToUpdate</targetReference>
        </connector>
        <flowName>Container_Opportunity_Evaluation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>updateRequested</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>opportunityContainerRecalculated</assignToReference>
            <name>reEvaluatedContainer</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Check_Splitted_Container_Status</name>
        <label>Check Splitted Container Status</label>
        <locationX>1106</locationX>
        <locationY>4838</locationY>
        <flowName>Check_Product_Container_Status</flowName>
        <inputAssignments>
            <name>isInputContainer</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>Get_Target_Container.Id</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Handle_Opportunity_Insert_Contact_History_Flow_1</name>
        <label>Handle Opportunity - Insert Contact History Flow 1</label>
        <locationX>1106</locationX>
        <locationY>3890</locationY>
        <connector>
            <targetReference>call_Recalculation_Sub_Flow_For_Container_Orig</targetReference>
        </connector>
        <flowName>HandleOpportunity_InsertContactHistory</flowName>
        <inputAssignments>
            <name>ContactHistoryList</name>
            <value>
                <elementReference>contactHistoryCollectionToInsert</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>recalculate_Container_Cloned</name>
        <label>recalculate Container Cloned</label>
        <locationX>1194</locationX>
        <locationY>4322</locationY>
        <connector>
            <targetReference>Bundle_Products</targetReference>
        </connector>
        <flowName>Container_Opportunity_Evaluation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>loop_For_Container_Cloned_To_Recalculate.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>updateRequested</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>opportunityContainerRecalculated</assignToReference>
            <name>reEvaluatedContainer</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>contactHistoryCollectionToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContactHistory__c</objectType>
    </variables>
    <variables>
        <name>countProductToClone</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>currentItem_filter_for_choosed_product</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>firstProduct</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>iDoppoProductCollectionToUpdate</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>indexLoopProduct</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>itemTemplateHistoryToUse</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ContactHistory__c</objectType>
    </variables>
    <variables>
        <name>openProductCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>openProductsCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>opportunityContainerRecalculated</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>opportunityContainerRecalculatedCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>opportunityContainerTemplateCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>opportunityProductToReparentCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>parentUsedCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>productChoosed</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>productCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>ProductLinea</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>productOpportunityCollectionReparentedToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>Products</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>templateOpportunityProductToReparent</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>templateRecordOpportunityContainer</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>varOpportunityIdText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
