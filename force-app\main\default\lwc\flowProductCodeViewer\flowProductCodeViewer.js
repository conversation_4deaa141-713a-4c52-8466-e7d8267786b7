import { LightningElement, api } from 'lwc';
import resolveOppId from '@salesforce/apex/ProductConfigService.resolveOppId';
import getProductCodeByOpp from '@salesforce/apex/ProductConfigService.getProductCodeByOpp';

export default class FlowProductCodeViewer extends LightningElement {
  _selectedOpportunityIdOrName;

  @api parentId; 

  loading = false;
  error = false;
  productCode;

  @api
  get selectedOpportunityId() {
    return this._selectedOpportunityIdOrName;
  }
  set selectedOpportunityId(val) {
    this._selectedOpportunityIdOrName = val;
    this.fetch();
  }

  get showPlaceholder() {
    return !this.loading && !this.error && !this.productCode;
  }

  isSalesforceId(value) {
    if (!value) return false;
    const len = value.length;
    if (len !== 15 && len !== 18) return false;
    return /^[A-Za-z0-9]+$/.test(value);
  }

  async fetch() {
    this.productCode = undefined;
    this.error = false;

    if (!this._selectedOpportunityIdOrName || this._selectedOpportunityIdOrName === 'all') {
      this.loading = false;
      return;
    }

    this.loading = true;
    try {
      let oppId = this._selectedOpportunityIdOrName;

      if (!this.isSalesforceId(oppId)) {
        oppId = await resolveOppId({
          key: this._selectedOpportunityIdOrName,
          parentId: this.parentId
        });
      }

      if (!oppId) {
        this.productCode = null;
        return;
      }

      const res = await getProductCodeByOpp({ oppId });
      this.productCode = res ? res.productCode : null;
      console.log('productCode:', this.productCode);
    } catch (e) {
      this.error = true;
      console.error(e);
    } finally {
      this.loading = false;
    }
  }
}