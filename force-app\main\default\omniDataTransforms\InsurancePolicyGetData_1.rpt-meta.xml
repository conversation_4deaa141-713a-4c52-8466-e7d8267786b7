<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DME_GetInsurancePolicyInfo&quot; : null,
    &quot;ResponseAction1&quot; : null,
    &quot;LA_FilterAuto&quot; : null,
    &quot;SV_setIPcalloutMock&quot; : null,
    &quot;IP_GetVehiculeInfo_Callout&quot; : null,
    &quot;DME_GetCaseInfo&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>InsurancePolicyGetData</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>InsurancePolicyGetDataCustom8268</globalKey>
        <inputFieldName>FolderId__c</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>InsurancePolicyGetDataCustom3056</globalKey>
        <inputFieldName>Position__c</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>ID</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>InsurancePolicyId</filterValue>
        <globalKey>InsurancePolicyGetDataCustom1128</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>InsurancePolicyGetDataCustom789</globalKey>
        <inputFieldName>ReferencePolicyNumber</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>InsurancePolicyGetDataCustom4237</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>InsurancePolicyGetDataCustom6292</globalKey>
        <inputFieldName>Product__c</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>InsurancePolicyGetData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;InsurancePolicyId&quot; : &quot;&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Turbo Extract</type>
    <uniqueName>InsurancePolicyGetData_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
