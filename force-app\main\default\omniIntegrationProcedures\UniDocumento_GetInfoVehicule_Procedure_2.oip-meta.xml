<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;ContextId&quot;: &quot;5009O00000e2OATQA2&quot;
}</customJavaScript>
    <description>UNF-800</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDocumento_GetInfoVehicule</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DME_GetCaseInfo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;CaseInfo|1&quot;,
  &quot;responseJSONNode&quot; : &quot;CaseInfo&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;CaseGetInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;CaseId&quot;,
    &quot;element&quot; : &quot;ContextId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTurboAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Turbo Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IP_GetName_Brand</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Code&quot; : &quot;%vehicle:brand%&quot;,
    &quot;DMName&quot; : &quot;BrandCodes_DM&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;DecisionMatrix_Handler&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IP_GetName_Model</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Code&quot; : &quot;%vehicle:model%&quot;,
    &quot;DMName&quot; : &quot;Model_Codes&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;DecisionMatrix_Handler&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LA_FilterAuto</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;vehicle&quot;,
  &quot;responseJSONNode&quot; : &quot;vehicle&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;mergeFields&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;hasPrimary&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;callout&quot; ],
  &quot;filterListFormula&quot; : &quot;=LIKE(positionNumber, %InsurancePolicyInfo:ReferencePolicyNumber%)&quot;,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ListAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;vehicle&quot; : &quot;%vehicle%&quot;,
    &quot;modelName&quot; : &quot;%IP_GetName_Model%&quot;,
    &quot;brandName&quot; : &quot;%IP_GetName_Brand%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : {
    &quot;vehicle&quot; : &quot;%vehicle%&quot;,
    &quot;modelName&quot; : &quot;%IP_GetName_Model:Model_Number%&quot;,
    &quot;brandName&quot; : &quot;%IP_GetName_Brand:Brand_Name%&quot;
  },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SV_GatherWith1GranularityLevel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;modelName&quot; : &quot;%IP_GetName_Model:Model_Number%&quot;,
    &quot;brandName&quot; : &quot;%IP_GetName_Brand:Brand_Name%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;vehicle&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SV_setIPcalloutMock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;fullResponse&quot; : &quot;=DESERIALIZE(&apos;{   \&quot;agencyCode\&quot;: \&quot;01853\&quot;,   \&quot;idTecPosition\&quot;: \&quot;****************_d00d5e6a-28ce-4307-b98d-d8ffbb96cae2\&quot;,   \&quot;response\&quot;: {     \&quot;positions\&quot;: [       {         \&quot;pet\&quot;: {},         \&quot;guarantees\&quot;: [           {             \&quot;premiums\&quot;: [               {                 \&quot;variables\&quot;: [                   {                     \&quot;value\&quot;: \&quot;false\&quot;,                     \&quot;key\&quot;: \&quot;discounted\&quot;                   }                 ],                 \&quot;grossPremium\&quot;: 38.16,                 \&quot;currency\&quot;: \&quot;Euro\&quot;,                 \&quot;ssn\&quot;: 0,                 \&quot;taxes\&quot;: 3.47,                 \&quot;netPremium\&quot;: 34.69,                 \&quot;flagQuantityDiscounted\&quot;: false,                 \&quot;flagDiscounted\&quot;: false,                 \&quot;type\&quot;: \&quot;ANN\&quot;               },               {                 \&quot;variables\&quot;: [                   {                     \&quot;value\&quot;: \&quot;false\&quot;,                     \&quot;key\&quot;: \&quot;discounted\&quot;                   }                 ],                 \&quot;grossPremium\&quot;: 38.16,                 \&quot;currency\&quot;: \&quot;Euro\&quot;,                 \&quot;ssn\&quot;: 0,                 \&quot;taxes\&quot;: 3.47,                 \&quot;netPremium\&quot;: 34.69,                 \&quot;flagQuantityDiscounted\&quot;: false,                 \&quot;flagDiscounted\&quot;: false,                 \&quot;type\&quot;: \&quot;RTS\&quot;               },               {                 \&quot;variables\&quot;: [                   {                     \&quot;value\&quot;: \&quot;true\&quot;,                     \&quot;key\&quot;: \&quot;discounted\&quot;                   }                 ],                 \&quot;grossPremium\&quot;: 35.3,                 \&quot;currency\&quot;: \&quot;Euro\&quot;,                 \&quot;ssn\&quot;: 0,                 \&quot;taxes\&quot;: 3.21,                 \&quot;netPremium\&quot;: 32.09,                 \&quot;flagQuantityDiscounted\&quot;: true,                 \&quot;flagDiscounted\&quot;: true,                 \&quot;type\&quot;: \&quot;ANN\&quot;               },               {                 \&quot;variables\&quot;: [                   {                     \&quot;value\&quot;: \&quot;true\&quot;,                     \&quot;key\&quot;: \&quot;discounted\&quot;                   }                 ],                 \&quot;grossPremium\&quot;: 35.3,                 \&quot;currency\&quot;: \&quot;Euro\&quot;,                 \&quot;ssn\&quot;: 0,                 \&quot;taxes\&quot;: 3.21,                 \&quot;netPremium\&quot;: 32.09,                 \&quot;flagQuantityDiscounted\&quot;: true,                 \&quot;flagDiscounted\&quot;: true,                 \&quot;type\&quot;: \&quot;RTS\&quot;               },               {                 \&quot;variables\&quot;: [                   {                     \&quot;value\&quot;: \&quot;true\&quot;,                     \&quot;key\&quot;: \&quot;discounted\&quot;                   }                 ],                 \&quot;grossPremium\&quot;: 0,                 \&quot;currency\&quot;: \&quot;Euro\&quot;,                 \&quot;ssn\&quot;: 0,                 \&quot;taxes\&quot;: 0,                 \&quot;netPremium\&quot;: 0,                 \&quot;flagQuantityDiscounted\&quot;: true,                 \&quot;flagDiscounted\&quot;: true,                 \&quot;type\&quot;: \&quot;RAT\&quot;               }             ],             \&quot;code\&quot;: \&quot;PUPASS\&quot;,             \&quot;id\&quot;: \&quot;dc619fea-b9cf-4538-8453-a542e3d3fa3b\&quot;           }         ],         \&quot;travel\&quot;: {},         \&quot;person\&quot;: {},         \&quot;home\&quot;: {},         \&quot;family\&quot;: {},         \&quot;vehicle\&quot;: {           \&quot;subjectRole\&quot;: \&quot;ASSICURATO\&quot;,           \&quot;model\&quot;: \&quot;4504\&quot;,           \&quot;brand\&quot;: \&quot;20\&quot;,           \&quot;id\&quot;: \&quot;1979f25c-3f0a-4bbc-9a5f-f3b6b5a06b0c\&quot;,           \&quot;licensePlate\&quot;: \&quot;AA000AA\&quot;         },         \&quot;notes\&quot;: [],         \&quot;titleEmissionDate\&quot;: \&quot;2025-06-30\&quot;,         \&quot;titleAmount\&quot;: 1044.54,         \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;,         \&quot;validityEndDate\&quot;: \&quot;2026-06-30\&quot;,         \&quot;positionNumber\&quot;: \&quot;*********\&quot;,         \&quot;idTecPosition\&quot;: \&quot;****************_d00d5e6a-28ce-4307-b98d-d8ffbb96cae2_0c91ac95-5444-4422-8088-1ee4efffc37c\&quot;,         \&quot;status\&quot;: \&quot;SIGILLATO\&quot;,         \&quot;productor\&quot;: \&quot; - \&quot;,         \&quot;productCode\&quot;: 9080,         \&quot;code\&quot;: \&quot;PUAUTO\&quot;,         \&quot;premiums\&quot;: [           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1069,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 126.43,             \&quot;netPremium\&quot;: 906.21,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1046.49,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 124.03,             \&quot;netPremium\&quot;: 886.1,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1046.49,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 124.03,             \&quot;netPremium\&quot;: 886.1,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 0,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 0,             \&quot;netPremium\&quot;: 0,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1.95,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 0.23,             \&quot;netPremium\&quot;: 1.72,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1.95,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 0.23,             \&quot;netPremium\&quot;: 1.72,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1069,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 126.43,             \&quot;netPremium\&quot;: 906.21,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;RTS\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1046.49,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 124.03,             \&quot;netPremium\&quot;: 886.1,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RTS\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 1046.49,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 36.36,             \&quot;taxes\&quot;: 124.03,             \&quot;netPremium\&quot;: 886.1,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RTS\&quot;           }         ],         \&quot;annualPremium\&quot;: [           1046.49         ],         \&quot;fractionation\&quot;: \&quot;1\&quot;       },       {         \&quot;pet\&quot;: {},         \&quot;travel\&quot;: {},         \&quot;person\&quot;: {},         \&quot;home\&quot;: {},         \&quot;family\&quot;: {           \&quot;relationType\&quot;: [             \&quot;CNT\&quot;           ],           \&quot;fiscalcode\&quot;: [             \&quot;****************\&quot;           ],           \&quot;lastname\&quot;: [             \&quot;BODINI\&quot;           ],           \&quot;firstname\&quot;: [             \&quot;FEDERICO\&quot;           ],           \&quot;ciu\&quot;: [             102           ],           \&quot;subjects\&quot;: [             {               \&quot;variables\&quot;: [                 {                   \&quot;value\&quot;: \&quot;Z000\&quot;,                   \&quot;key\&quot;: \&quot;nazioneNascita\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;1969-07-04T11:00:00.000Z\&quot;,                   \&quot;key\&quot;: \&quot;dataNascita\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;true\&quot;,                   \&quot;key\&quot;: \&quot;flagCohabitation\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;Coniuge/compagno\&quot;,                   \&quot;key\&quot;: \&quot;familyRoleType\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;D\&quot;,                   \&quot;key\&quot;: \&quot;tipoOccupazione\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;1\&quot;,                   \&quot;key\&quot;: \&quot;codiceCompagnia\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;102\&quot;,                   \&quot;key\&quot;: \&quot;ciu\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;0\&quot;,                   \&quot;key\&quot;: \&quot;parentId\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;false\&quot;,                   \&quot;key\&quot;: \&quot;conducenteAbituale\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;SPF\&quot;,                   \&quot;key\&quot;: \&quot;tipoPersonaGiuridica\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;D\&quot;,                   \&quot;key\&quot;: \&quot;flagProvvisorioDefinitivo\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;true\&quot;,                   \&quot;key\&quot;: \&quot;numeroAddetti\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;9740\&quot;,                   \&quot;key\&quot;: \&quot;professione\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;CR\&quot;,                   \&quot;key\&quot;: \&quot;provinciaNascita\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;D\&quot;,                   \&quot;key\&quot;: \&quot;tipoAnagrafica\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;D150\&quot;,                   \&quot;key\&quot;: \&quot;comuneNascitaBelfiore\&quot;                 },                 {                   \&quot;value\&quot;: \&quot;0\&quot;,                   \&quot;key\&quot;: \&quot;id\&quot;                 }               ],               \&quot;roles\&quot;: [                 {                   \&quot;percentage\&quot;: 0,                   \&quot;relationType\&quot;: \&quot;FAM\&quot;,                   \&quot;relatedId\&quot;: \&quot;65ad8af4-0a51-4972-8a16-1beb9ec9fc62\&quot;,                   \&quot;relatingId\&quot;: \&quot;16add598-9605-47c3-aa2a-1dc758b0d121\&quot;,                   \&quot;relatedRoleType\&quot;: \&quot;CON\&quot;,                   \&quot;relatingRoleType\&quot;: \&quot;RFA\&quot;,                   \&quot;id\&quot;: \&quot;ec371db2-9402-4626-8d36-2a34bf820100\&quot;,                   \&quot;creationEventId\&quot;: \&quot;1a4c736d-9970-4f54-b9d1-29862e5443d2\&quot;,                   \&quot;tmstEndRegistration\&quot;: \&quot;9999-12-31T23:59:59.999Z\&quot;,                   \&quot;tmstInitRegistration\&quot;: \&quot;2025-06-30T12:11:00.71Z\&quot;,                   \&quot;effectiveEndDate\&quot;: \&quot;9999-12-31\&quot;,                   \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;                 },                 {                   \&quot;percentage\&quot;: 0,                   \&quot;relationType\&quot;: \&quot;CNT\&quot;,                   \&quot;relatedId\&quot;: \&quot;16add598-9605-47c3-aa2a-1dc758b0d121\&quot;,                   \&quot;relatingId\&quot;: \&quot;16add598-9605-47c3-aa2a-1dc758b0d121\&quot;,                   \&quot;relatedRoleType\&quot;: \&quot;NCN\&quot;,                   \&quot;relatingRoleType\&quot;: \&quot;CAF\&quot;,                   \&quot;id\&quot;: \&quot;32afa7cb-da8d-4fc3-a94e-1c1b248301ca\&quot;,                   \&quot;creationEventId\&quot;: \&quot;1a4c736d-9970-4f54-b9d1-29862e5443d2\&quot;,                   \&quot;tmstEndRegistration\&quot;: \&quot;9999-12-31T23:59:59.999Z\&quot;,                   \&quot;tmstInitRegistration\&quot;: \&quot;2025-06-30T12:11:00.71Z\&quot;,                   \&quot;effectiveEndDate\&quot;: \&quot;9999-12-31\&quot;,                   \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;                 }               ],               \&quot;role\&quot;: {                 \&quot;percentage\&quot;: 0,                 \&quot;relationType\&quot;: \&quot;CNT\&quot;,                 \&quot;relatedId\&quot;: \&quot;16add598-9605-47c3-aa2a-1dc758b0d121\&quot;,                 \&quot;relatingId\&quot;: \&quot;16add598-9605-47c3-aa2a-1dc758b0d121\&quot;,                 \&quot;relatedRoleType\&quot;: \&quot;NCN\&quot;,                 \&quot;relatingRoleType\&quot;: \&quot;CAF\&quot;,                 \&quot;id\&quot;: \&quot;32afa7cb-da8d-4fc3-a94e-1c1b248301ca\&quot;,                 \&quot;creationEventId\&quot;: \&quot;1a4c736d-9970-4f54-b9d1-29862e5443d2\&quot;,                 \&quot;tmstEndRegistration\&quot;: \&quot;9999-12-31T23:59:59.999Z\&quot;,                 \&quot;tmstInitRegistration\&quot;: \&quot;2025-06-30T12:11:00.71Z\&quot;,                 \&quot;effectiveEndDate\&quot;: \&quot;9999-12-31\&quot;,                 \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;               },               \&quot;address\&quot;: {                 \&quot;address\&quot;: \&quot;ROMA\&quot;,                 \&quot;civicNumber\&quot;: \&quot;1\&quot;,                 \&quot;province\&quot;: \&quot;TORINO\&quot;,                 \&quot;locality\&quot;: \&quot;TORINO\&quot;,                 \&quot;municipality\&quot;: \&quot;TORINO\&quot;,                 \&quot;lon\&quot;: \&quot;7.68457\&quot;,                 \&quot;lat\&quot;: \&quot;45.07055\&quot;,                 \&quot;shortDescription\&quot;: \&quot;V. ROMA\&quot;,                 \&quot;description\&quot;: \&quot;VIA ROMA\&quot;,                 \&quot;provinceCode\&quot;: \&quot;TO\&quot;,                 \&quot;regionIstatCode\&quot;: \&quot;001\&quot;,                 \&quot;municipalityIstatCode\&quot;: \&quot;001272\&quot;,                 \&quot;dugCode\&quot;: \&quot;VIA\&quot;,                 \&quot;municipalityCadastralCode\&quot;: \&quot;L219\&quot;,                 \&quot;zipCode\&quot;: \&quot;10123\&quot;,                 \&quot;type\&quot;: \&quot;RESI\&quot;,                 \&quot;id\&quot;: \&quot;7b6f9c71-733d-4618-9ba2-46899d11b162\&quot;,                 \&quot;creationEventId\&quot;: \&quot;1a4c736d-9970-4f54-b9d1-29862e5443d2\&quot;,                 \&quot;tmstEndRegistration\&quot;: \&quot;9999-12-31T23:59:59.999Z\&quot;,                 \&quot;tmstInitRegistration\&quot;: \&quot;2025-06-30T12:11:00.710Z\&quot;,                 \&quot;effectiveEndDate\&quot;: \&quot;9999-12-31\&quot;,                 \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;               },               \&quot;contacts\&quot;: [                 {                   \&quot;email\&quot;: \&quot;<EMAIL>\&quot;,                   \&quot;phoneNumber\&quot;: \&quot;+393388644887\&quot;                 }               ],               \&quot;subjectType\&quot;: \&quot;PF\&quot;,               \&quot;gender\&quot;: \&quot;M\&quot;,               \&quot;fiscalCodeType\&quot;: \&quot;N\&quot;,               \&quot;flagCorporate\&quot;: false,               \&quot;authorityPublic\&quot;: \&quot;false\&quot;,               \&quot;customSubjectRole\&quot;: \&quot;ASSICURATO\&quot;,               \&quot;birthDate\&quot;: \&quot;1969-07-04\&quot;,               \&quot;privacyCode\&quot;: \&quot;06\&quot;,               \&quot;fiscalCode\&quot;: \&quot;****************\&quot;,               \&quot;lastName\&quot;: \&quot;BODINI\&quot;,               \&quot;firstName\&quot;: \&quot;FEDERICO\&quot;,               \&quot;phoneNumber\&quot;: \&quot;+393388644887\&quot;,               \&quot;email\&quot;: \&quot;<EMAIL>\&quot;,               \&quot;ciu\&quot;: 102,               \&quot;companyFormCode\&quot;: 0,               \&quot;id\&quot;: \&quot;e037a688-cb4f-4613-9840-3f88ddbcbc2f\&quot;,               \&quot;creationEventId\&quot;: \&quot;1a4c736d-9970-4f54-b9d1-29862e5443d2\&quot;,               \&quot;tmstEndRegistration\&quot;: \&quot;9999-12-31T23:59:59.999Z\&quot;,               \&quot;tmstInitRegistration\&quot;: \&quot;2025-06-30T12:11:00.656Z\&quot;,               \&quot;effectiveEndDate\&quot;: \&quot;9999-12-31\&quot;,               \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;             }           ],           \&quot;subjectRole\&quot;: \&quot;ASSICURATO\&quot;,           \&quot;id\&quot;: [             \&quot;e037a688-cb4f-4613-9840-3f88ddbcbc2f\&quot;           ]         },         \&quot;vehicle\&quot;: {},         \&quot;notes\&quot;: [],         \&quot;titleEmissionDate\&quot;: \&quot;2025-06-30\&quot;,         \&quot;titleAmount\&quot;: 69.48,         \&quot;effectiveStartDate\&quot;: \&quot;2025-06-30\&quot;,         \&quot;validityEndDate\&quot;: \&quot;2026-06-30\&quot;,         \&quot;positionNumber\&quot;: \&quot;1148192174705\&quot;,         \&quot;idTecPosition\&quot;: \&quot;****************_d00d5e6a-28ce-4307-b98d-d8ffbb96cae2_78c23bed-3761-4684-bbb5-0eb545d10982\&quot;,         \&quot;status\&quot;: \&quot;SIGILLATO\&quot;,         \&quot;productor\&quot;: \&quot; - \&quot;,         \&quot;productCode\&quot;: 7266,         \&quot;code\&quot;: \&quot;PUFAM\&quot;,         \&quot;premiums\&quot;: [           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 85.53,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 15.12,             \&quot;netPremium\&quot;: 70.41,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 70.2,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.41,             \&quot;netPremium\&quot;: 57.79,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 69.48,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.29,             \&quot;netPremium\&quot;: 57.19,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;ANN\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 85.53,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 15.12,             \&quot;netPremium\&quot;: 70.41,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 70.2,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.41,             \&quot;netPremium\&quot;: 57.79,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 69.48,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.29,             \&quot;netPremium\&quot;: 57.19,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RAT\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 85.53,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 15.12,             \&quot;netPremium\&quot;: 70.41,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: false,             \&quot;type\&quot;: \&quot;RTS\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 70.2,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.41,             \&quot;netPremium\&quot;: 57.79,             \&quot;flagQuantityDiscounted\&quot;: false,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RTS\&quot;           },           {             \&quot;variables\&quot;: [],             \&quot;grossPremium\&quot;: 69.48,             \&quot;currency\&quot;: \&quot;Euro\&quot;,             \&quot;ssn\&quot;: 0,             \&quot;taxes\&quot;: 12.29,             \&quot;netPremium\&quot;: 57.19,             \&quot;flagQuantityDiscounted\&quot;: true,             \&quot;flagDiscounted\&quot;: true,             \&quot;type\&quot;: \&quot;RTS\&quot;           }         ],         \&quot;annualPremium\&quot;: [           69.48         ],         \&quot;fractionation\&quot;: \&quot;1\&quot;       }     ],     \&quot;upselling\&quot;: [],     \&quot;ownership\&quot;: \&quot;null\&quot;,     \&quot;fea\&quot;: \&quot;FALSE\&quot;,     \&quot;expirationDate\&quot;: \&quot;2026-06-30\&quot;,     \&quot;policyStatus\&quot;: \&quot;SIGILLATO\&quot;,     \&quot;int\&quot;: \&quot;3\&quot;,     \&quot;subAgencyDescription\&quot;: \&quot;BDC TEST SCORPORTO\&quot;,     \&quot;subAgency\&quot;: \&quot;319a802d-d8e7-4d1b-98ea-d9ce05f41b9a\&quot;,     \&quot;cip\&quot;: 23,     \&quot;channelCode\&quot;: \&quot;AGENZIA\&quot;,     \&quot;productName\&quot;: \&quot;UNICA\&quot;,     \&quot;fiscalCode\&quot;: \&quot;****************\&quot;,     \&quot;subjectId\&quot;: \&quot;65ad8af4-0a51-4972-8a16-1beb9ec9fc62\&quot;,     \&quot;productType\&quot;: \&quot;PU\&quot;,     \&quot;policyNumber\&quot;: \&quot;101853000308917\&quot;   } }&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;fullResponse:response:positions&quot;,
  &quot;responseJSONNode&quot; : &quot;callout&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>failure condition formula : Product__c != &quot;9080&quot;</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>DME_GetInsurancePolicyInfo</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;insurancePolicy|1&quot;,
  &quot;responseJSONNode&quot; : &quot;InsurancePolicyInfo&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;InsurancePolicyGetData&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;InsurancePolicyId&quot;,
    &quot;element&quot; : &quot;CaseInfo:Insurance_Policy__c&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTurboAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Turbo Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TryCatchBlock1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;failureResponse&quot; : {
    &quot;failureResponse&quot; : &quot;the Insurance Policy of this Case is not linked to an Auto insurance (product 9080)&quot;
  },
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnBlockError&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;TryCatchBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Try Catch Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IP_GetVehiculeInfo_Callout</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;response:status != 200&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;callout&quot;,
  &quot;sendJSONPath&quot; : &quot;CaseInfo:Insurance_Policy__c&quot;,
  &quot;sendJSONNode&quot; : &quot;policyId&quot;,
  &quot;integrationProcedureKey&quot; : &quot;ProdottiAssicurativiDetails_GetDataRow&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TryCatchBlock3</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;failureResponse&quot; : {
    &quot;failureResponse&quot; : &quot;callout failed : ProdottiAssicurativiDetails_GetDataRow&quot;
  },
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnBlockError&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;TryCatchBlock3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Try Catch Block</type>
    </omniProcessElements>
    <omniProcessKey>UniDocumento_GetInfoVehicule</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>GetInfoVehicule</subType>
    <type>UniDocumento</type>
    <uniqueName>UniDocumento_GetInfoVehicule_Procedure_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <webComponentKey>aca58643-9bc7-524a-b6e3-70ef20fcdfd4</webComponentKey>
</OmniIntegrationProcedure>
