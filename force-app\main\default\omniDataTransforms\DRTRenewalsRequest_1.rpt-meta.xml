<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;period&quot; : &quot;week&quot;,
  &quot;fromDate&quot; : &quot;2025-01-01&quot;,
  &quot;toDate&quot; : &quot;&quot;,
  &quot;referent&quot; : &quot;****************&quot;,
  &quot;section&quot; : &quot;RCA;REL&quot;,
  &quot;rate&quot; : &quot;true&quot;,
  &quot;startWeek&quot; : &quot;2025-07-21&quot;,
  &quot;startMonth&quot; : &quot;2025-07-01&quot;,
  &quot;agencies&quot; : [ &quot;101853&quot;, &quot;404567&quot; ]
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;compagnia&quot; : &quot;1&quot;,
  &quot;agenzia&quot; : [ &quot;101853&quot;, &quot;404567&quot; ],
  &quot;dataDa&quot; : &quot;2025-01-01&quot;,
  &quot;dataA&quot; : &quot;2025-06-30&quot;,
  &quot;comparto&quot; : [ &quot;RCA&quot;, &quot;REL&quot;, &quot;VIT&quot; ],
  &quot;referente&quot; : [ &quot;****************&quot; ],
  &quot;frazionamentoMensile&quot; : true
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DRTRenewalsRequest</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:period &quot;fromTo&quot; = var:dateTo | | | TODAY YEAR &quot;-&quot; | | | TODAY MONTH 10 &lt; | &quot;0&quot; | | TODAY MONTH CONCAT | | TODAY MONTH IF &quot;-&quot; | | | TODAY DAY 10 &lt; | &quot;0&quot; | | TODAY DAY CONCAT | | TODAY DAY IF CONCAT IF</formulaConverted>
        <formulaExpression>IF(
  period = &quot;fromTo&quot;,
  dateTo,
  CONCAT(
    YEAR(TODAY()), &quot;-&quot;,
    IF(MONTH(TODAY()) &lt; 10, CONCAT(&quot;0&quot;, MONTH(TODAY())), MONTH(TODAY())), &quot;-&quot;,
    IF(DAY(TODAY()) &lt; 10, CONCAT(&quot;0&quot;, DAY(TODAY())), DAY(TODAY()))
  )
)</formulaExpression>
        <formulaResultPath>toDate</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom642</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom1589</globalKey>
        <inputFieldName>toDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom9349</globalKey>
        <inputFieldName>agency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;String&gt;</outputFieldFormat>
        <outputFieldName>agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom8385</globalKey>
        <inputFieldName>sectionList</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;String&gt;</outputFieldFormat>
        <outputFieldName>comparto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:sectionFormula &quot;;&quot; SPLIT</formulaConverted>
        <formulaExpression>SPLIT(sectionFormula, &quot;;&quot;)</formulaExpression>
        <formulaResultPath>sectionList</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom6127</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:compagnia &quot;;&quot; SPLIT</formulaConverted>
        <formulaExpression>SPLIT(compagnia, &quot;;&quot;)</formulaExpression>
        <formulaResultPath>compagniaList</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom5632</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:period &quot;today&quot; = | | | TODAY YEAR &quot;-&quot; | | | TODAY MONTH 10 &lt; | &quot;0&quot; | | TODAY MONTH CONCAT | | TODAY MONTH IF &quot;-&quot; | | | TODAY DAY 10 &lt; | &quot;0&quot; | | TODAY DAY CONCAT | | TODAY DAY IF CONCAT | var:period &quot;week&quot; = var:startWeek | var:period &quot;month&quot; = var:startMonth var:fromDate
 IF IF IF</formulaConverted>
        <formulaExpression>IF(
  period = &quot;today&quot;,
  CONCAT(
    YEAR(TODAY()), &quot;-&quot;,
    IF(MONTH(TODAY()) &lt; 10, CONCAT(&quot;0&quot;, MONTH(TODAY())), MONTH(TODAY())), &quot;-&quot;,
    IF(DAY(TODAY()) &lt; 10, CONCAT(&quot;0&quot;, DAY(TODAY())), DAY(TODAY()))
  ),
  IF(
    period = &quot;week&quot;,
    startWeek,
    IF(
      period = &quot;month&quot;,
      startMonth,
      fromDate
    )
  )
)</formulaExpression>
        <formulaResultPath>fromDate</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom9889</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:referent &quot;;&quot; SPLIT</formulaConverted>
        <formulaExpression>SPLIT(referent, &quot;;&quot;)</formulaExpression>
        <formulaResultPath>referentList</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom3635</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom3386</globalKey>
        <inputFieldName>referentList</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;String&gt;</outputFieldFormat>
        <outputFieldName>referente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:section &quot;&quot; = &quot;RCA;REL;VIT&quot; var:section IF</formulaConverted>
        <formulaExpression>IF(section = &quot;&quot;, &quot;RCA;REL;VIT&quot;, section )</formulaExpression>
        <formulaResultPath>sectionFormula</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>DRTRenewalsRequestCustom9315</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom7734</globalKey>
        <inputFieldName>compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom8097</globalKey>
        <inputFieldName>rate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>frazionamentoMensile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsRequestCustom7014</globalKey>
        <inputFieldName>fromDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsRequest</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>dataDa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;SetContext&quot; : {
    &quot;startMonth&quot; : &quot;2025-07-01&quot;,
    &quot;startWeek&quot; : &quot;2025-07-21&quot;
  },
  &quot;section&quot; : &quot;RCA;VIT&quot;,
  &quot;options&quot; : {
    &quot;forceQueueable&quot; : false,
    &quot;mockHttpResponse&quot; : null,
    &quot;vlcApexResponse&quot; : true,
    &quot;useFuture&quot; : false,
    &quot;isTestProcedure&quot; : false,
    &quot;resetCache&quot; : false,
    &quot;integrationProcedureKey&quot; : null,
    &quot;vlcIPData&quot; : null,
    &quot;OmniAnalyticsTrackingDebug&quot; : false,
    &quot;ignoreCache&quot; : true,
    &quot;shouldCommit&quot; : false,
    &quot;vlcTestSuiteUniqueKey&quot; : null,
    &quot;vlcTestUniqueKey&quot; : null,
    &quot;vlcCacheKey&quot; : null,
    &quot;continuationStepResult&quot; : null,
    &quot;vlcFilesMap&quot; : null,
    &quot;ParentInteractionToken&quot; : null,
    &quot;useQueueable&quot; : false,
    &quot;disableMetadataCache&quot; : false,
    &quot;isDebug&quot; : true,
    &quot;queueableChainable&quot; : false,
    &quot;useContinuation&quot; : false,
    &quot;chainable&quot; : false,
    &quot;ignoreMetadataPermissions&quot; : false,
    &quot;useHttpCalloutMock&quot; : false,
    &quot;useQueueableApexRemoting&quot; : false
  },
  &quot;toDate&quot; : &quot;21-07-2025&quot;,
  &quot;startMonth&quot; : &quot;2025-07-01&quot;,
  &quot;rate&quot; : &quot;true&quot;,
  &quot;referent&quot; : &quot;****************;****************&quot;,
  &quot;SetContextStatus&quot; : true,
  &quot;period&quot; : &quot;fromTo&quot;,
  &quot;startWeek&quot; : &quot;2025-07-21&quot;,
  &quot;fromDate&quot; : &quot;21-05-2025&quot;,
  &quot;compagnia&quot; : &quot;4&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DRTRenewalsRequest_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
