<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CaseNumber</fieldItem>
                <identifier>RecordCaseNumberField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-7138fa36-f76f-4bc3-a109-08e766beddf1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Indicatore_SLA__c</fieldItem>
                <identifier>RecordIndicatore_SLA_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-bf5f7659-226c-4ae6-aa0c-cf12cc38c414</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Case.urcs_CasePresaInCarico</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2 AND 3</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Risolto</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Annullato</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa risposta cliente</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                                                <valueListItems>
                            <value>Case.urcs_CaseModificaE2C</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>ur_CaseES</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>In gestione</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.urcs_CaseAttesaTerzeParti</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Nuova richiesta</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Mancata risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Ricevuta risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa terze parti</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Trasferito</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Risolto</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Annullato</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.urcs_CaseTrasferimento</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Nuova richiesta</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Mancata risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Ricevuta risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa terze parti</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Risolto</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Annullato</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Trasferito</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.urcs_CaseChiusura</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2 AND 3 AND 4</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Risolto</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Chiuso - Annullato</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>In attesa risposta cliente</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Trasferito</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Case.urcs_CasesSbloccoManuale</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.Status}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>In attesa risposta cliente</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>primaryField</name>
                    <value>Facet-7138fa36-f76f-4bc3-a109-08e766beddf1</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>secondaryFields</name>
                    <value>Facet-bf5f7659-226c-4ae6-aa0c-cf12cc38c414</value>
                </componentInstanceProperties>
                <componentName>record_flexipage:dynamicHighlights</componentName>
                <identifier>record_flexipage_dynamicHighlights</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideUpdateButton</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>variant</name>
                    <value>linear</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_pathassistant:pathAssistant</componentName>
                <identifier>runtime_sales_pathassistant_pathAssistant</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CaseNumber</fieldItem>
                <identifier>RecordCaseNumberField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UfficioApertura__c</fieldItem>
                <identifier>RecordUfficioApertura_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UtAssegnatario__c</fieldItem>
                <identifier>RecordUtAssegnatario__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-15cf7a8f-bccb-4286-8ca8-a42e7829ec16</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AccountId</fieldItem>
                <identifier>RecordAccountIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DriverId__c</fieldItem>
                <identifier>RecordDriverId_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ContactId</fieldItem>
                <identifier>RecordContactIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EntChiamante__c</fieldItem>
                <identifier>RecordEntit_chiamante_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SubAgente__c</fieldItem>
                <identifier>RecordSubAgente_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.EntChiamante__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Agente</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NomeChiamante__c</fieldItem>
                <identifier>RecordNomeChiamante_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EmailChiamante__c</fieldItem>
                <identifier>RecordEmailChiamante_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TelChiamante__c</fieldItem>
                <identifier>RecordTelChiamante_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-0c99b1ec-3080-4307-9a00-64066370bb06</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-15cf7a8f-bccb-4286-8ca8-a42e7829ec16</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0c99b1ec-3080-4307-9a00-64066370bb06</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9b8ba2ba-4447-42af-9164-8654e2d3b3b2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Origin</fieldItem>
                <identifier>RecordOriginField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TipoRichiesta__c</fieldItem>
                <identifier>RecordTipoRichiesta_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Categoria__c</fieldItem>
                <identifier>RecordCategoria_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SottoCategoria__c</fieldItem>
                <identifier>RecordSottoCategoria_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Sollecitato__c</fieldItem>
                <identifier>RecordSollecitato_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Macrostato__c</fieldItem>
                <identifier>RecordMacrostato_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b7b60dd8-531f-4c7a-9957-8a7a06ab5da4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ContractAsset__c</fieldItem>
                <identifier>RecordContractAsset_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Subject</fieldItem>
                <identifier>RecordSubjectField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Description</fieldItem>
                <identifier>RecordDescriptionField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Zona_di_circolazione__c</fieldItem>
                <identifier>RecordZona_di_circolazione_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NumSolleciti__c</fieldItem>
                <identifier>RecordNumSolleciti_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EmailSollecito__c</fieldItem>
                <identifier>RecordEmailSollecito_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.EmailSollecito__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.InvioEmailAutomatico__c</fieldItem>
                <identifier>RecordInvioEmailAutomatico_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2f7d1e5a-2133-4653-a0f6-f6d8f1edc7f5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b7b60dd8-531f-4c7a-9957-8a7a06ab5da4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2f7d1e5a-2133-4653-a0f6-f6d8f1edc7f5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b917068e-d2b4-48eb-900d-df92055edd5f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NoteChiusura__c</fieldItem>
                <identifier>RecordNoteChiusura_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2 OR 3 OR 4 OR 5</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseCRM</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseSitoWeb</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CasePQ</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseAR</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseES</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NoteChiusuraCliente__c</fieldItem>
                <identifier>RecordNoteChiusuraCliente_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseAR</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NoteChiusuraAgente__c</fieldItem>
                <identifier>RecordNoteChiusuraAgente_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CasePQ</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dffedbd9-8777-4a2b-97eb-d57679beaa8e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClosedDate</fieldItem>
                <identifier>RecordClosedDateField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Risolto__c</fieldItem>
                <identifier>RecordRisolto_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseCRM</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c7ab6652-51c9-4c12-8755-7ad29e95d53e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dffedbd9-8777-4a2b-97eb-d57679beaa8e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c7ab6652-51c9-4c12-8755-7ad29e95d53e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2d072be5-a843-4208-85c4-65dc3f7ad8a5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CodAgenzia__c</fieldItem>
                <identifier>RecordCodAgenzia_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Agenzia__c</fieldItem>
                <identifier>RecordAgenzia_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EmailAgente__c</fieldItem>
                <identifier>RecordEmailAgente_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SubAgente__c</fieldItem>
                <identifier>RecordSubAgente_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2706c192-76dd-4282-8340-d388e91ea1ce</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CodAgente__c</fieldItem>
                <identifier>RecordCodAgente_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Agente__c</fieldItem>
                <identifier>RecordAgente_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TelAgente__c</fieldItem>
                <identifier>RecordTelAgente_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-98dcd54a-afa2-4288-b1aa-5fb714228efa</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2706c192-76dd-4282-8340-d388e91ea1ce</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-98dcd54a-afa2-4288-b1aa-5fb714228efa</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-77ce000d-c801-4e93-9cfb-555ca7e4cb1a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CaseCRM</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3dda8d7d-b40f-43b5-96fe-cda52ee2d5e5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedDate</fieldItem>
                <identifier>RecordCreatedDateField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedDate</fieldItem>
                <identifier>RecordLastModifiedDateField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-75a263a9-242a-49ad-95a4-f9aed56e2457</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-3dda8d7d-b40f-43b5-96fe-cda52ee2d5e5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-75a263a9-242a-49ad-95a4-f9aed56e2457</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-07469b87-3cd3-4447-bfe1-c9e0bb1c4f00</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-9b8ba2ba-4447-42af-9164-8654e2d3b3b2</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Informazioni Case</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-b917068e-d2b4-48eb-900d-df92055edd5f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettaglio Case</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-2d072be5-a843-4208-85c4-65dc3f7ad8a5</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Dettagli Chiusura</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-77ce000d-c801-4e93-9cfb-555ca7e4cb1a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Agenzia</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>ur_CasePQ</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-07469b87-3cd3-4447-bfe1-c9e0bb1c4f00</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Informazioni di Sistema</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-15b3282b-3687-4b03-b3c0-145297d7f03e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-15b3282b-3687-4b03-b3c0-145297d7f03e</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ad66c4db-848e-416f-9211-05df3f4c2386</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-ad66c4db-848e-416f-9211-05df3f4c2386</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>context</name>
                    <value>RECORD</value>
                </componentInstanceProperties>
                <componentName>forceChatter:publisher</componentName>
                <identifier>forceChatter_publisher</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>context</name>
                    <value>RECORD</value>
                </componentInstanceProperties>
                <componentName>forceChatter:exposedFeed</componentName>
                <identifier>forceChatter_exposedFeed</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f1806f05-9fac-4051-8fe6-9ac6055910cb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-1236ca6e-b0b0-4f18-b229-61a9dae6f0dd</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Case.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentNotes</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>30</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f154ef81-3979-4f7f-bb73-1e1be15b618a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Case.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentDocuments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d3af0024-9dda-425a-a91b-4c18e0a81c59</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f1806f05-9fac-4051-8fe6-9ac6055910cb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.feed</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1236ca6e-b0b0-4f18-b229-61a9dae6f0dd</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.eduActivities</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f154ef81-3979-4f7f-bb73-1e1be15b618a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Note</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d3af0024-9dda-425a-a91b-4c18e0a81c59</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Files</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-072aac3c-9c6a-477f-a51b-0e10a116afe8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>Case.ContractAsset__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>titleFieldName</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>ContractAsset__c.urcs_ContractAssetOnCase</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ContractAsset__r.NomeContratto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>Case.ContractAsset__c.Asset__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>Asset.urcs_AssetOnCase</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ContractAsset__r.NomeContratto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>lookupFieldName</name>
                    <value>Case.ContractAsset__c.ServiceContract__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>titleFieldName</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>updateQuickActionName</name>
                    <value>ServiceContract.urcs_ServiceContractOnCase</value>
                </componentInstanceProperties>
                <componentName>console:relatedRecord</componentName>
                <identifier>console_relatedRecord2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ContractAsset__r.NomeContratto__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>decorate</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>richTextValue</name>
                    <value>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 20px;&quot;&gt;Nessuna informazione da mostrare&lt;/strong&gt;&lt;/p&gt;</value>
                </componentInstanceProperties>
                <componentName>flexipage:richText</componentName>
                <identifier>flexipage_richText</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ContractAsset__r.NomeContratto__c}</leftValue>
                        <operator>EQUAL</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-4c375e79-d0da-42de-9469-44642de2fe44</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4c375e79-d0da-42de-9469-44642de2fe44</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Ulteriori Informazioni</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2726363a-a325-4526-9d5d-bf07e2a8ede5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Case.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fcc60966-9723-4b10-969b-3e92f017efe6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fcc60966-9723-4b10-969b-3e92f017efe6</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>History Tracking</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-309f6a44-1ef3-46a2-ae3a-222c26e4d5d3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideMarkCompleteButton</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>support:milestones</componentName>
                <identifier>support_milestones</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-072aac3c-9c6a-477f-a51b-0e10a116afe8</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-2726363a-a325-4526-9d5d-bf07e2a8ede5</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-309f6a44-1ef3-46a2-ae3a-222c26e4d5d3</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset4</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>urcs_Case</masterLabel>
    <sobjectType>Case</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
