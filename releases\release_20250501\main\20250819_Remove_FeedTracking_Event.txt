------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
The purpose of this manual procedure is how to Disable Feed Tracking for the Event Object in Salesforce (Manual Steps).
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 

1. Log in to your target Salesforce org
   - Use your credentials to access the org where you want to disable Feed Tracking for the Event object.

2. Go to Setup
   - Click the gear icon (⚙️) in the top right corner and select "Setup".

3. Search for Feed Tracking
   - In the Quick Find box (left sidebar), type "Feed Tracking" and select the "Feed Tracking" option under "Feature Settings" > "Chatter".

4. Select the Event Object
   - In the list of objects, scroll down and click on "Event".

5. Disable Feed Tracking
   - Uncheck the "Enable Feed Tracking" checkbox at the top of the page.
   - If you see a list of tracked fields, they will be disabled automatically when you uncheck this option.

6. Save the Changes
   - Click the "Save" button to apply your changes.

7. Verify
   - Open a record of type Event.
   - The "Follow" button (Chatter feed) should no longer be visible.