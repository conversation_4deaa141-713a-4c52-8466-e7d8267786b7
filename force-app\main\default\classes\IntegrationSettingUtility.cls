/**
 * @File Name         : IntegrationSettingUtility.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 28-02-2025
 * @Last Modified By  : <EMAIL>
**/
public without sharing class IntegrationSettingUtility /* implements Callable */ {

	// Custom exception class for integration setting errors
	public class IntegrationSettingErrorException extends Exception {}

	/* //Method for Callable Implementation
	public Object call(String action, Map<String, Object> args)
    {
        System.debug(args);
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');
        
        String integrationId = (String)input.get('integrationId');
        String body = (String)input.get('body');
        Object result = JSON.deserializeUntyped(IntegrationSettingUtility.executeHttpRequest(integrationId, body).body);
        output.put('result', result);
        return result;
    } 
	*/

	// Static List and list for caching named credentials and integrationSettings__mtd
	public static Map<String,IntegrationSettings__mdt> integrationSettingMap = new Map<String,IntegrationSettings__mdt>();

	// Wrapper class for HTTP response
	public class HttpResponseWrapper {
		@InvocableVariable
		@AuraEnabled
		public String body;
		@InvocableVariable
		@AuraEnabled
		public Integer statusCode;
		@InvocableVariable
		@AuraEnabled
		public String status;

		public HttpResponseWrapper(HttpResponse response) {	
		this.body = response.getBody();
		this.statusCode = response.getStatusCode();
		this.status = response.getStatus();
		}
	}

	// Wrapper class for HTTP input from flow
	public class FlowHttpWrapper {
		@InvocableVariable
		public String integrationId;
		@InvocableVariable
		public String body;
		@InvocableVariable
		public String jsonQueryPathParams;

		// No-arg constructor (required for Flow)
		public FlowHttpWrapper() {}

	
		// Constructor
		public FlowHttpWrapper(String integrationId, String body, String jsonQueryPathParams) {
			this.integrationId = integrationId;
			this.body = body;
			this.jsonQueryPathParams = jsonQueryPathParams;
		}
	} 
	
	@InvocableMethod(label='Execute HTTP Request from Flow' description='Execute HTTP Request from Flow')
	public static List<HttpResponseWrapper> executeHttpRequestFromFlow(List<FlowHttpWrapper> flowHttpWrapperList) {
		return new List<HttpResponseWrapper> {executeHttpRequest(flowHttpWrapperList[0].integrationId, flowHttpWrapperList[0].body,flowHttpWrapperList[0].jsonQueryPathParams)};
	}

	@AuraEnabled
	public static HttpResponseWrapper executeHttpRequest(String integrationId, String body, String jsonQueryPathParams) {
		
		// Cache integrationSettings__mtd records
		IntegrationSettings__mdt intSetting = getIntegrationMTD(integrationId);
		String endpointComposition = '';
		
		//Check if endpoint and method are set
		if(String.isBlank(intSetting.Endpoint__c)){
			throw new IntegrationSettingErrorException('Endpoint, not set for integration ID: ' + integrationId);
		}else{
			endpointComposition = intSetting.Endpoint__c;
		}
		//Check jsonQueryParam if not null
		if(jsonQueryPathParams != null && !String.isBlank(jsonQueryPathParams)){
			try {							
					// Deserializza il JSON in una mappa non tipizzata
					Map<String, Object> queryPathParamsMap = (Map<String, Object>) JSON.deserializeUntyped(jsonQueryPathParams);

					// Accedi al nodo "Params"
					if (queryPathParamsMap.containsKey('Params')) {
						Map<String, Object> paramsMap = (Map<String, Object>) queryPathParamsMap.get('Params');
						
						// Accedi al nodo "QueryParam"
						if (paramsMap.containsKey('QueryParam')) {
							Map<String, Object> queryParamMap = (Map<String, Object>) paramsMap.get('QueryParam');
							Integer count = 0;
							for (String key : queryParamMap.keySet()) {
								if(count == 0){
									endpointComposition += '?';
								}else {
									endpointComposition += '&';
								}
								System.debug('QueryParam - Key: ' + key + ', Value: ' + queryParamMap.get(key)?.ToString());
								endpointComposition += key + '=' + (queryParamMap.get(key) == null ? '' : queryParamMap.get(key)?.ToString().replace(' ', '+'));
								count++;
							}
							System.debug('Final Endpoint with query params: ' + endpointComposition);
						}
						// Accedi al nodo "PathParam"
						if (paramsMap.containsKey('PathParam')) {
							Map<String, Object> pathParamMap = (Map<String, Object>) paramsMap.get('PathParam');
							for (String key : pathParamMap.keySet()) {
								System.debug('PathParam - Key: ' + key + ', Value: ' + pathParamMap.get(key));
								String placeholder = '%' + key + '%';
								String value = (String) pathParamMap.get(key)?.ToString().replace(' ', '+');
								endpointComposition = endpointComposition.replace(placeholder, value);
							}
							System.debug('Final Endpoint with path params: ' + endpointComposition);
						}
						
						System.debug('Final Endpoint with path params or query params: ' + endpointComposition);
					} else {
						System.debug('The "Params" node is not present in the JSON. Therefore, the endpoint will not handle queryParams and pathParams, remaining as configured.');
					}
				} catch (Exception e) {
					System.debug('>>>> ' + e.getStackTraceString());
					throw new IntegrationSettingErrorException('Callout error: ' + e.getMessage());
				}

		}else {
			System.debug('The passed parameter "jsonQueryPathParams" does not contain valid JSON to process. Therefore, the endpoint will not handle queryParams and pathParams, remaining as configured.');
		}



		

		// make request 
		HttpRequest request = new HttpRequest();
		Map<String,String> uiids = new Map<String,String>();
		uiids.putAll(getTransactionRequestUUID());

		//Check if endpoint and method are set
		if(String.isBlank(intSetting.Endpoint__c) || String.isBLank(intSetting.Method__c) || String.isBlank(intSetting.NamedCredential__c)){
			throw new IntegrationSettingErrorException('Endpoint,Method or Named Credential not set for integration ID: ' + integrationId);
		}else{

			if(!String.valueOf(intSetting.Method__c).equals('GET') ){ // && !String.valueOf(intSetting.Method__c).equals('DELETE')
				// Check if body is set for non-GET methods
				if (String.isBlank(body)) {
					throw new IntegrationSettingErrorException('There has to be a body for this call : ' + intSetting.IntegrationId__c);
				}else{
					request.setBody(body);
				}
			}
			
			request.setEndpoint('callout:' +intSetting.NamedCredential__c + endpointComposition);
			request.setMethod(intSetting.Method__c);

			// Set headers and timeout
			Map<String, Object> queryPathParamsMap = (jsonQueryPathParams != null && !String.isBlank(jsonQueryPathParams)) ? (Map<String, Object>) JSON.deserializeUntyped(jsonQueryPathParams) : new Map<String, Object>();			
			Map<String, Object> paramsMap = (queryPathParamsMap.containsKey('Params')) ? (Map<String, Object>) queryPathParamsMap.get('Params') : new Map<String, Object>();
			Map<String, Object> headerParamMap = (paramsMap.containsKey('HeaderParams')) ? (Map<String, Object>) paramsMap.get('HeaderParams') : new Map<String, Object>();
			if(intSetting.Header__c != null){
				Map<String, Object> headersValueByKeyMap = (Map<String, Object>) JSON.deserializeUntyped(intSetting.Header__c);
				for(String headerKey :headersValueByKeyMap.keySet()) {
				// Check if headerKey exists in headerParamMap
					if(headerParamMap.containsKey(headerKey)) {
						System.debug('Setting existing header: ' + headerKey);
						request.setHeader(headerKey, (String) headerParamMap.get(headerKey));
						System.debug('Header ' + headerKey + ' Value: ' + request.getHeader(headerKey));
					} else {
						System.debug('Setting existing header: ' + headerKey);
						request.setHeader(headerKey, (String) headersValueByKeyMap.get(headerKey));
						System.debug('Header ' + headerKey + ' Value: ' + request.getHeader(headerKey));
					}
				}
			}else{
				System.debug('IntegrationSettingUtility.executeHttpRequest: Header__c is not set. No headers will be set');
			}

			// Volodia Gounaris (2025-08-11) : add this header key for outbound logging into dynatrace
			try{

				System.debug('IntegrationSettingUtility.executeHttpRequest: Generated UUIDs: ' + uiids);
				request.setHeader('X-SFDC-REQUEST-ID', uiids.get('RequestId'));
				System.debug('IntegrationSettingUtility.executeHttpRequest: Added "X-SFDC-REQUEST-ID" header with value: ' + request.getHeader('X-SFDC-REQUEST-ID'));
			}
			catch(Exception exc){UniLogger.writeError('Failed to add "X-SFDC-REQUEST-ID" in the callout header', exc);}

			if(intSetting.TimeoutMs__c != null && Integer.valueOf(intSetting.TimeoutMs__c) > 0 && Integer.valueOf(intSetting.TimeoutMs__c) <= 120000) {
				request.setTimeout(120000);
			}else{
				System.debug('IntegrationSettingUtility.executeHttpRequest: TimeoutMs__c is not set or invalid. Setting default timeout');
			}
		}

		System.debug('IntegrationSettingUtility.executeHttpRequest: Request: ' + request.getBody());

		// execution request
		try 
		{
			Http http = new Http();
			HttpResponse response = http.send(request);

			// log the callout request & response
			try{
                UniLogger logEvent = new Unilogger('integration framework happy flow', request, response, integrationId, uiids.get('uuid'));
                logEvent
                    .setlogSource(Unilogger.logSource.Apex)
					.setLoggingLevel(LoggingLevel.INFO)
                    .publishLog();
            } catch(Exception exc){}

			return new HttpResponseWrapper(response);
		}catch(System.CalloutException e){

			// log the error
			try{
                Unilogger.writeError('failure with this integration', e);
            } catch(Exception exc){}

			// Handle callout exceptions
			throw new IntegrationSettingErrorException('Callout error: ' + e.getMessage());
		}
	}

	// Get integrationSettings__mtd record AND cache it
	public static IntegrationSettings__mdt getIntegrationMTD(String integrationId) {
		IntegrationSettings__mdt intSetting;
		
		if(integrationSettingMap.isEmpty() || !integrationSettingMap.isEmpty() && integrationSettingMap.get(integrationId) == null ){
			List<IntegrationSettings__mdt> integrationSettingListExecute = [SELECT Endpoint__c, Header__c, Method__c, NamedCredential__c, TimeoutMs__c, IntegrationId__c, TemplateJSONBody__c
																	FROM IntegrationSettings__mdt
																	WHERE IntegrationId__c = :integrationId
																	AND IsActive__c = TRUE
																	ORDER BY Version__c DESC
																];
			if(integrationSettingListExecute.isEmpty()){
				throw new IntegrationSettingErrorException('No active integration settings found for integration ID: ' + integrationId);
			}else if(integrationSettingListExecute.size() != 1){
				throw new IntegrationSettingErrorException('Multiple active integration settings found for integration ID: ' + integrationId);
			}else{
				intSetting = integrationSettingListExecute[0];
				integrationSettingMap.put(integrationId, intSetting);
			}
		}else{
			System.debug('IntegrationSettingUtility.executeHttpRequestFromFlow: Integration settings already cached.');
			intSetting = integrationSettingMap.get(integrationId);
		}

		return intSetting;
	}


	// this returns the Salesforce transaction identifier universal key
	// it can cross-validate with Event Log File (ELF) data
	// https://developer.salesforce.com/docs/atlas.en-us.apexref.meta/apexref/apex_class_System_Request.htm
	// Volodia Gounaris 2025-08-11
	public static Map<String,String> getTransactionRequestUUID(){
		Request reqInfo = Request.getCurrent();
		String output = EncodingUtil.convertToHex(
			Crypto.generateDigest(
				'MD5', 
				Blob.valueOf(reqInfo.getRequestId())
			)
		);
		UUID randomUUID = UUID.randomUUID();
		system.debug(randomUUID); // Prints the UUID string that was randomly generated
		return new Map<String,String>{
			'RequestId' => reqInfo.getRequestId(),
			'hexRequestId' => output,
			'uuid' => randomUUID.toString()
		};
	}


}