public with sharing class ProductConfigService {

    @TestVisible private static final String LOG = '[ProductConfigService] ';

    public class ProductConfigDTO {
        @AuraEnabled public String productCode;
        @AuraEnabled public String domainType;

        public override String toString() {
            return 'DTO{productCode=' + productCode + ', domainType=' + domainType + '}';
        }
    }

    // === NEW: risolve un oppId partendo da Id o Name ===
    @AuraEnabled(cacheable=true)
    public static Id resolveOppId(String key, Id parentId) {
        System.debug(LOG + 'resolveOppId key=' + key + ', parentId=' + parentId);
        if (String.isBlank(key)) return null;

        // se sembra già un Id SF (15/18 alfanum), lo ritorno
        if (Pattern.matches('^[a-zA-Z0-9]{15}(?:[a-zA-Z0-9]{3})?$', key)) {
            System.debug(LOG + 'Key è già un SFID, lo uso diretto.');
            return (Id) key;
        }

        // altrimenti è un Name: cerco l’opportunity per Nome (meglio scoped sul parent)
        List<Opportunity> opps;
        if (parentId != null) {
            opps = [
                SELECT Id
                FROM Opportunity
                WHERE Name = :key
                  AND Parent__c = :parentId
                ORDER BY LastModifiedDate DESC
                LIMIT 1
            ];
        } else {
            opps = [
                SELECT Id
                FROM Opportunity
                WHERE Name = :key
                ORDER BY LastModifiedDate DESC
                LIMIT 1
            ];
        }

        if (opps.isEmpty()) {
            System.debug(LOG + 'Nessuna Opportunity trovata per Name=' + key + ' (parent=' + parentId + ')');
            return null;
        }
        System.debug(LOG + 'Trovata Opportunity: ' + opps[0].Id);
        return opps[0].Id;
    }

    @AuraEnabled(cacheable=true)
    public static ProductConfigDTO getProductCodeByOpp(Id oppId) {
        System.debug(LOG + 'Invocazione getProductCodeByOpp, oppId=' + oppId);

        ProductConfigDTO dto = new ProductConfigDTO();

        if (oppId == null) {
            System.debug(LOG + 'oppId nullo: ritorno DTO vuoto.');
            return dto;
        }

        try {
            List<Opportunity> opps = [
                SELECT Id, DomainType__c
                FROM Opportunity
                WHERE Id = :oppId
                LIMIT 1
            ];
            if (opps.isEmpty()) {
                System.debug(LOG + 'Nessuna Opportunity trovata per Id=' + oppId + '.');
                return dto;
            }
            Opportunity opp = opps[0];
            dto.domainType = opp.DomainType__c;
            System.debug(LOG + 'Opportunity trovata. DomainType__c=' + dto.domainType);

            if (String.isBlank(dto.domainType)) {
                System.debug(LOG + 'DomainType__c vuoto/nullo. Stop.');
                return dto;
            }

            List<ProductConfiguration__mdt> cfgs = [
                SELECT ProductCode__c, DomainType__c
                FROM ProductConfiguration__mdt
                WHERE DomainType__c = :dto.domainType
                LIMIT 1
            ];
            if (cfgs.isEmpty()) {
                System.debug(LOG + 'Nessuna ProductConfiguration__mdt per DomainType__c=' + dto.domainType + '.');
                return dto;
            }

            dto.productCode = cfgs[0].ProductCode__c;
            System.debug(LOG + 'ProductConfiguration trovata. ProductCode__c=' + dto.productCode);
            System.debug(LOG + 'DTO finale: ' + dto);
            return dto;

        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, LOG + 'ERRORE: ' + e.getMessage() + ' | ' + e.getStackTraceString());
            return dto;
        }
    }
}