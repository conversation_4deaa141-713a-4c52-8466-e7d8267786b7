<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>CaseButtonPushMissingDocument</label>
    <protected>false</protected>
    <values>
        <field>BodyType__c</field>
        <value xsi:type="xsd:string">JSON</value>
    </values>
    <values>
        <field>CallbackUrl__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DelayMs__c</field>
        <value xsi:type="xsd:double">1000.0</value>
    </values>
    <values>
        <field>Endpoint__c</field>
        <value xsi:type="xsd:string">/api/v1/contracts/%contractId%/documents/%documentId%/documentsources</value>
    </values>
    <values>
        <field>Header__c</field>
        <value xsi:type="xsd:string">{&quot;X-CHANNEL-SUBJECT&quot;: &quot;101853X1&quot;,  &quot;X-UNIPOL-CHANNEL&quot;: &quot;SFDC&quot;, &quot;X-UNIPOL-REQUESTID&quot;: &quot;5810423a-ddf1-4205-a7cb-b187367012f6&quot;, &quot;client_num&quot;: &quot;46a42a50ba4f47a4b2d27c8ab9d3757f&quot;, &quot;client_secret&quot;: &quot;C18690D6f3AE4Afb9c208f&quot;}</value>
    </values>
    <values>
        <field>IntegrationId__c</field>
        <value xsi:type="xsd:string">CaseButtonPushMissingDocument</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>Log_Lifetime__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Method__c</field>
        <value xsi:type="xsd:string">POST</value>
    </values>
    <values>
        <field>NamedCredential__c</field>
        <value xsi:type="xsd:string">ExperienceFSCAPI</value>
    </values>
    <values>
        <field>Protocol__c</field>
        <value xsi:type="xsd:string">REST</value>
    </values>
    <values>
        <field>PublishLog__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>TemplateJSONBody__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>TimeoutMs__c</field>
        <value xsi:type="xsd:double">2000.0</value>
    </values>
    <values>
        <field>Type__c</field>
        <value xsi:type="xsd:string">Synch</value>
    </values>
    <values>
        <field>Version__c</field>
        <value xsi:type="xsd:double">1.0</value>
    </values>
</CustomMetadata>
