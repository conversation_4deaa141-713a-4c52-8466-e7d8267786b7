<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assignment_isZonaCircRequired</name>
        <label>Assignment isZonaCircRequired</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>isZonaCircrRequired</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <name>Check_Categoria</name>
        <label>Check Categoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Auto_in_preassegnazione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Auto in preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_7_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Auto in preassegnazione</label>
        </rules>
        <rules>
            <name>Auto_sostitutiva</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Auto sostitutiva</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_sottocategoria</targetReference>
            </connector>
            <label>Auto sostitutiva</label>
        </rules>
        <rules>
            <name>Circolazione_all_estero</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Circolazione all&apos;estero</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Circolazione all&apos;estero</label>
        </rules>
        <rules>
            <name>Contratto</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Contratto</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Contratto</label>
        </rules>
        <rules>
            <name>Gestione_interventi</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Gestione interventi</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_3_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Gestione interventi</label>
        </rules>
        <rules>
            <name>Gestione_sinistri</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Gestione sinistri</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_4_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Gestione sinistri</label>
        </rules>
        <rules>
            <name>Logistica_veicolo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Logistica veicolo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_5_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Logistica veicolo</label>
        </rules>
        <rules>
            <name>Soccorso_e_recupero</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Soccorso e recupero</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_6_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Soccorso e recupero</label>
        </rules>
        <rules>
            <name>Reclamo_Chiusura_Contratto</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Chiusura Contratto</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_8_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Chiusura Contratto</label>
        </rules>
        <rules>
            <name>Reclamo_Consegna_Veicolo_Nuovo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Consegna Veicolo Nuovo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_9_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Consegna Veicolo Nuovo</label>
        </rules>
        <rules>
            <name>Reclamo_Consegna_Veicolo_Preassegnazione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Consegna Veicolo Preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_10_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Consegna Veicolo Preassegnazione</label>
        </rules>
        <rules>
            <name>Reclamo_Consegna_Veicolo_Sostitutiva</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Consegna Veicolo Sostitutiva</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_11_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Consegna Veicolo Sostitutiva</label>
        </rules>
        <rules>
            <name>Reclamo_Manutenzione_Veicolo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Manutenzione Veicolo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_12_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Manutenzione Veicolo</label>
        </rules>
        <rules>
            <name>Reclamo_Pneumatici</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Pneumatici</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_13_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Pneumatici</label>
        </rules>
        <rules>
            <name>Reclamo_Sinistro_carrozzeria</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Sinistro - carrozzeria</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_14_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Sinistro - carrozzeria</label>
        </rules>
        <rules>
            <name>Reclamo_Soccorso_Stradale</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Categoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reclamo Soccorso Stradale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_15_of_Check_sottocategoria</targetReference>
            </connector>
            <label>Reclamo Soccorso Stradale</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta auto sostitutiva in seguito a intervento programmabile</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta auto sostitutiva in seguito a veicolo in panne</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_10_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_10_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Categoria Inferiore</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Condizioni veicolo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_11_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_11_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Categoria Inferiore</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Condizioni veicolo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_12_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_12_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tempistiche di riparazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Contestazione riaddebito riparazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Qualità Riparazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_13_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_13_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo sostituzione pneumatici</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Sostituzione pneumatici non soddisfacente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo trasferimento su fornitore</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pneumatici -Contestazione Fattura</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_14_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_14_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Tempi di riparazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Contestazione riaddebito Penali</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Qualità riparazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata autorizzazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_15_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_15_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancato Soccorso</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata erogazione veicolo sostitutivo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta informazioni e procedure di assistenza tecnica all&apos;estero</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta informazioni e procedure di Soccorso e recupero all&apos;estero</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni e procedure di assistenza tecnica all&apos;estero</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni e procedure di Soccorso e recupero all&apos;estero</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_2_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Chiusura Anticipata Contratto</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Conguaglio periodico</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Conguaglio finale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_3_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_3_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta stato avanzamento intervento</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritiro veicolo da fine intervento/riconsegna sostitutiva</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta Trasferimento gomme tramite Corriere</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni centri convenzionati</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta trasferimento gomme in autonomia</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Verifica centro di stoccaggio gomme</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni e procedure per richiamo casa madre</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni e procedure per prenotazione intervento programmabile</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni procedura trasferimento gomme</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_4_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_4_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Inserimento sinistro completo per conto del cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>INFORMAZIONI PROCEDURE IN SEGUITO A FURTO DEL VEICOLO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_5_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_5_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Conferma appuntamento per ritiro veicolo nuovo in seguito a MAD (messa a disposizione)</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta informazioni arrivo veicolo nuovo</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni per riconsegna veicolo per fine noleggio</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RICHIESTA RIFERIMENTI PUNTO DI CONSEGNA VEICOLO NUOVO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_6_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_6_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta rimborso</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni/procedure di assistenza in seguito a veicolo in panne</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Intervento a supporto vs fornitore soccorso (fornitore)</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Informazioni e procedure per richiesta rimborso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_7_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_7_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta informazioni arrivo veicolo in preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta appuntamento ritiro veicolo in preassegnazione - MAD ricevuta</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta appuntamento riconsegna veicolo in preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Nuova richiesta veicoli in preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Richiesta cambio veicolo in preassegnazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_8_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_8_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Danni rientro-Contestazione Fattura</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Conguaglio km Contestazione Fattura</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Elementi mancanti al rientro - Cont. Fattura</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Auto restituita - Ritardo interruzione fatturazione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_9_of_Check_sottocategoria</name>
        <label>Check sottocategoria</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_9_of_is_required</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ritardo consegna</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Categoria Inferiore</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Sottocategoria</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Condizioni veicolo</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_isZonaCircRequired</targetReference>
            </connector>
            <label>is required</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>urcs_CaseCheckZonaCircRequired {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseCheckZonaCircRequired</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Categoria</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>Categoria</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isZonaCircrRequired</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>Sottocategoria</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
