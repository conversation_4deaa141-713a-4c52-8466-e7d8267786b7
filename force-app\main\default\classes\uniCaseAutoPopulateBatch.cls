global class uniCaseAutoPopulateBatch implements Database.Batchable<SObject>, Database.Stateful {

    // Cache statiche per performance
    private static Id attivitaContattoRecordTypeId;
    private static Map<String, Schema.SObjectField> caseFieldsCache;
    public Datetime sixHoursAgo = System.now().addHours(-6);
    
    static {
        // Inizializza cache al caricamento della classe
        initializeCache();
    }
    
    private static void initializeCache() {
        attivitaContattoRecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
        caseFieldsCache = Case.SObjectType.getDescribe().fields.getMap();
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {

        DateTime sixHoursAgostart = this.sixHoursAgo;

        String baseFields = 'Id, Status, AccountId, Priority, AreaOfNeed__c, AssignedTo__c, Agency__c, AssignedGroup__c, Area__c, Detail__c, ' +
            'CCEngaged__c, DueDate__c, Insurance_Policy__c, Requestfromclient__c, Activity__c, Created_Date__c, LeoActivityCode__c, Nature__c, Source__c, ' +
            'ExternalId__c, isCCEnabled__c, BusinessKey__c, ClosedDate__c, StartDate__c, RecordTypeId, RecordType.DeveloperName, OwnerId ';
        
        String techFields = 'TECH_LeoHeat__c, TECH_LeoPriority__c, TECH_NeedsCloseCallout__c, TECH_AssignmentRules__c, TECH_getInTouch__c, ' +
            'TECH_PossibleAssignemnt__c, TECH_RequiredIncident__c, TECH_RequiredPolicy__c, TECH_ShowCloseManual__c, CorrelationId__c,' +
            'TECH_ShowOutcome__c, TECH_IsPlannable__c, TECH_ShowButtonMissingDocument__c, TECH_ShowButtonCertificaModifica__c, TECH_ShowButtonDownloadDoc__c, TECH_ShowButtonSendSMS__c ';

        String drFields = 'DrAgenziaFiglia__c, DrAgenziaMadre__c, DrAnnotazione__c, DrAssegnatarioDesc__c, DrAssegnatario__c, ' +
            'DrAssegnazione__c, DrCfPivaCliente__c, DrCodice__c, DrDominio__c, DrPosizioniPU__c, DrIdFolderPU__c, ' +
            'DrCompagnia__c, DrIdAttivitaLeonardo__c, DrContextEntity__c, DrFlagAnnotazione__c, DrCanale__c';

        String query = 'SELECT ' + baseFields + ', ' + techFields + ', ' + drFields +
            ' FROM Case WHERE CreatedDate >= :sixHoursAgostart AND DrDominio__c != null AND DrCodice__c != null' +
            ' AND (RecordTypeId = null OR RecordType.DeveloperName = \'AttivitaContatto\')';

        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext bc, List<Case> scope) {
        if (scope == null || scope.isEmpty()) {
            return;
        }

        CaseFilteringResult preFilterResult = preFilterCasesForLeoCodes(scope);
        
        if (preFilterResult.leoCodes.isEmpty()) {
            return;
        }
        Map<Integer, CaseActivityInit__c> caseActInitMap = getCaseActivityInits(preFilterResult.leoCodes);
        
        if (caseActInitMap.isEmpty()) {
            return;
        }

        List<Case> validCases = filterCasesWithActivityInit(preFilterResult.candidateCases, caseActInitMap);
        
        if (validCases.isEmpty()) {
            return;
        }

        List<Case> processedCases = processAndEnrichCasesOptimized(validCases, caseActInitMap);
        updateCasesWithHandling(processedCases);
    }

    global void finish(Database.BatchableContext bc) {
        Integer batchSize = 30;
        Database.executeBatch(new ManageCaseBatch(this.sixHoursAgo), batchSize); //richiamo al batch per gestire i coni
    }

    // Classe wrapper per risultato pre-filtering
    private class CaseFilteringResult {
        public List<Case> candidateCases = new List<Case>();
        public Set<String> leoCodes = new Set<String>();
    }

    private CaseFilteringResult preFilterCasesForLeoCodes(List<Case> scope) {
        CaseFilteringResult result = new CaseFilteringResult();
        
        for (Case c : scope) {
            if (c.DrDominio__c != null && c.DrCodice__c != null) {
                String leo = c.DrDominio__c + c.DrCodice__c;
                result.leoCodes.add(leo);
                result.candidateCases.add(c);
                
                // Pre-populate LeoActivityCode per evitare calcolo successivo
                c.LeoActivityCode__c = leo;
            }
        }
        
        System.debug('Candidate cases found: ' + result.candidateCases.size());
        System.debug('Leo codes collected: ' + result.leoCodes.size());
        return result;
    }
    
    private List<Case> filterCasesWithActivityInit(List<Case> candidateCases, Map<Integer, CaseActivityInit__c> caseActInitMap) {
        List<Case> validCases = new List<Case>();
        
        for (Case c : candidateCases) {
            if (c.DrDominio__c != null && c.DrCodice__c != null) {
                String leo = c.DrDominio__c + c.DrCodice__c;
                if (caseActInitMap.containsKey(Integer.valueOf(leo))) {
                    validCases.add(c);
                }
            }
        }
        
        System.debug('Valid cases after CaseActivityInit filtering: ' + validCases.size());
        return validCases;
    }

    private Set<String> collectLeoCodes(List<Case> scope) {
        Set<String> leoCode = new Set<String>();
        for (Case c : scope) {
            if (c.DrDominio__c != null && c.DrCodice__c != null) {
                System.debug('Processing Case with DrDominio__c: ' + c.DrDominio__c + ' and DrCodice__c: ' + c.DrCodice__c);
                String leo = c.DrDominio__c + c.DrCodice__c;
                leoCode.add(leo);
            }
        }
        system.debug('Leo codes collected: ' + leoCode.size());
        System.debug('Leo codes: ' + leoCode);
        return leoCode;
    }

    private static Map<Integer, CaseActivityInit__c> getCaseActivityInits(Set<String> leoCodes) {
        List<CaseActivityInit__c> caseActInitList = [SELECT Name, LeoCode__c, NeedsCloseCallout__c, Source__c, CCEngaged__c, PossibleAssignemnt__c,OwnerId,CorrelationId__c,
            AssignmentRules__c, LeoHeat__c, LeoPriority__c, ShowOutcome__c, IsPlannable__c, Nature__c, Area__c, ClosedDate__c, Activity__c, Detail__c, AreaOfNeed__c, DueDateDays__c, IsCallBack__c, IsReservedArea__c,
            RequiredPolicy__c, RequiredIncident__c, GetInTouch__c, ShowCloseManual__c, OverrideAgecy__c, Show_Button_Missing_Doc__c, Show_Button_Certifica_Modifica__c, Show_Button_Download_Doc__c, Show_Button_Send_SMS__c 
            FROM CaseActivityInit__c WHERE OverrideAgecy__c = null AND LeoCode__c IN :leoCodes AND Type__c = 'Contact'];

        Map<Integer, CaseActivityInit__c> caseActivityMap = new Map<Integer, CaseActivityInit__c>();
        for (CaseActivityInit__c caseActivity : caseActInitList) {
            Integer leo = Integer.valueOf(caseActivity.LeoCode__c);
            if (!caseActivityMap.containsKey(leo)) {
                caseActivityMap.put(leo, caseActivity);
            }
        }
        system.debug('CaseActivityInit__c records found: ' + caseActivityMap.size());
        System.debug('CaseActivityInit__c records: ' + caseActivityMap);
        return caseActivityMap;
    }

    private List<Case> processAndEnrichCasesOptimized(List<Case> validCases, Map<Integer, CaseActivityInit__c> caseActInitMap) {
        List<Case> processedCases = processExternalCasesOptimized(validCases, caseActInitMap);
        
        CaseRulesBeforeInsert.populateAgencyOnAutomaticCase(processedCases);
        CaseRulesBeforeInsert.setCasesPriority(processedCases);
        CaseRulesBeforeInsert.extractBusinessKeyToPopulateInsurancePolicy(processedCases);
        CaseRulesAssignementBI.manageCaseGroupsOwnership(processedCases);
        CaseRulesAssignementBI.checkPolicy(processedCases);
        
        processStatusUpdatesOptimized(processedCases);
        
        System.debug('Cases after processing: ' + processedCases.size());
        return processedCases;
    }
    
    private void processStatusUpdatesOptimized(List<Case> cases) {
        List<Case> newStatusCases = new List<Case>();
        
        // Filtra solo i casi con Status 'New' in un singolo loop
        for (Case c : cases) {
            if (c.Status == 'New') {
                newStatusCases.add(c);
            }
        }
        
        if (!newStatusCases.isEmpty()) {
            CaseRulesBeforeInsert.updateStatusToExpiredDueDate(newStatusCases);
            CaseRulesBeforeInsert.updateStatusToClosed(newStatusCases);
        }
    }

    private static List<Case> processExternalCasesOptimized(List<Case> validCases, Map<Integer, CaseActivityInit__c> caseActInitMap) {
        List<Case> casesToUpdate = new List<Case>();
        
        for (Case c : validCases) {
            c.LeoActivityCode__c = (c.DrDominio__c != null && c.DrCodice__c != null) ? c.DrDominio__c + c.DrCodice__c : null;
            if (c.LeoActivityCode__c != null && caseActInitMap.containsKey(Integer.valueOf(c.LeoActivityCode__c))) {
                System.debug('Matching CaseActivityInit__c found for LeoActivityCode: ' + c.LeoActivityCode__c);
                CaseActivityInit__c matchedRecord = caseActInitMap.get(Integer.valueOf(c.LeoActivityCode__c));
                CaseRulesBeforeInsert.populateFieldsIfEmpty(c, matchedRecord);
            }
            
            c.StartDate__c = (c.StartDate__c == null) ? System.today() : c.StartDate__c;
            c.RecordTypeId = (c.RecordTypeId == null) ? attivitaContattoRecordTypeId : c.RecordTypeId;
            
            casesToUpdate.add(c);
        }
        
        System.debug('Cases processed: ' + casesToUpdate.size());
        return casesToUpdate;
    } 
    
    private void updateCasesWithHandling(List<Case> caseAfterInit) {
        if (caseAfterInit != null && !caseAfterInit.isEmpty()) {
            List<Case> casesToDelete = new List<Case>();
            List<Case> casesToUpdate = new List<Case>();
            splitCasesByAccountId(caseAfterInit, casesToDelete, casesToUpdate);
            handleCaseDeletes(casesToDelete);
            handleCase(casesToUpdate);
        }    
    }

    private void splitCasesByAccountId(List<Case> cases, List<Case> casesToDelete, List<Case> casesToUpdate) {
        for (Case c : cases) {
            if (c.AccountId == null) {
                casesToDelete.add(c);
            } else {
                casesToUpdate.add(c);
            }
        }
    }

    private void handleCaseDeletes(List<Case> casesToDelete) {
        if (casesToDelete.isEmpty()) {
            return;
        }
        
        try {
            Database.DeleteResult[] deleteResults = Database.delete(casesToDelete, false);
            
            // Log degli errori di eliminazione
            List<Database.DeleteResult> failedDeletes = new List<Database.DeleteResult>();
            for (Integer i = 0; i < deleteResults.size(); i++) {
                Database.DeleteResult result = deleteResults[i];
                if (!result.isSuccess()) {
                    failedDeletes.add(result);
                    System.debug(LoggingLevel.ERROR, 'Error deleting Case with Id: ' + casesToDelete[i].Id + ' - ' + result.getErrors());
                }
            }
            
            if (!failedDeletes.isEmpty()) {
                UniLogger.writeERROR('Failed to delete Cases: ', failedDeletes);
            }
        } catch (Exception ex) {
            System.debug(LoggingLevel.ERROR, 'Exception during Case delete: ' + ex.getMessage());
            UniLogger.writeERROR('Exception during Case delete: ', ex);
        }
    }

    private void handleCase(List<Case> casesToUpdate) {
        if (casesToUpdate.isEmpty()) {
            return;
        }
        
        try {
            Database.SaveResult[] results = Database.update(casesToUpdate, false);
            
            // Ottimizzazione: usa un loop diretto invece di indexOf
            List<Database.SaveResult> failedResults = new List<Database.SaveResult>();
            for (Integer i = 0; i < results.size(); i++) {
                Database.SaveResult result = results[i];
                if (!result.isSuccess()) {
                    failedResults.add(result);
                    System.debug(LoggingLevel.ERROR, 'Error updating Case with Id: ' + casesToUpdate[i].Id + ' - ' + result.getErrors());
                }
            }
            
            if (!failedResults.isEmpty()) {
                UniLogger.writeERROR('Failed to update Cases: ', failedResults);
            }
        } catch (Exception ex) {
            System.debug(LoggingLevel.ERROR, 'Exception during Case update: ' + ex.getMessage());
            UniLogger.writeERROR('Exception during Case update: ', ex);
        }
    }

    private static void logCaseUpdateError(Case c, Database.SaveResult result) {
        System.debug('Error updating Case with Id: ' + c.Id + ' - ' + result.getErrors()[0].getMessage());
    }
}